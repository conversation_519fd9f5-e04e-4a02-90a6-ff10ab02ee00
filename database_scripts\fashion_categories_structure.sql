-- Start transaction for safety
START TRANSACTION;
-- First, delete any existing fashion-related categories to avoid conflicts
DELETE FROM category_translations
WHERE category_id BETWEEN 700 AND 999;
DELETE FROM categories
WHERE id BETWEEN 700 AND 999;
-- Create main Fashion & Accessories category
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        700,
        0,
        0,
        'Fashion & Accessories',
        1,
        0.00,
        'uploads/categories/banner/fashion-banner.jpg',
        'uploads/categories/icon/fashion-icon.png',
        1,
        1,
        0,
        'fashion-accessories',
        'Fashion & Accessories',
        'Complete fashion collection for women, men, kids, and babies',
        NOW(),
        NOW()
    );
-- Add translations for main category
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES (700, 'Fashion & Accessories', 'en', NOW(), NOW()),
    (700, 'Mode & Accessoires', 'fr', NOW(), NOW()),
    (700, 'الأزياء والإكسسوارات', 'sa', NOW(), NOW());
-- 1. Women's Category (Level 1)
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        701,
        700,
        1,
        'Women',
        1,
        0.00,
        'uploads/categories/banner/women-banner.jpg',
        'uploads/categories/icon/women-icon.png',
        1,
        1,
        0,
        'women',
        'Women\'s Fashion',
        'Women\'s clothing, footwear, and accessories',
        NOW(),
        NOW()
    );
-- Women's Clothing (Level 2)
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        702,
        701,
        2,
        'Women\'s Clothing',
        1,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'womens-clothing',
        'Women\'s Clothing',
        'Complete collection of women\'s clothing',
        NOW(),
        NOW()
    );
-- Women's Clothing Subcategories (Level 3)
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES -- Dresses and Skirts
    (
        703,
        702,
        3,
        'Dresses',
        1,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'dresses',
        'Dresses',
        'All types of dresses',
        NOW(),
        NOW()
    ),
    (
        704,
        702,
        3,
        'Skirts',
        2,
        0.00,
        NULL,
        NULL,
        1,
        0,
        0,
        'skirts',
        'Skirts',
        'Stylish skirts collection',
        NOW(),
        NOW()
    ),
    -- Pants and Shorts
    (
        705,
        702,
        3,
        'Pants',
        3,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'pants',
        'Pants',
        'Women\'s pants and trousers',
        NOW(),
        NOW()
    ),
    (
        706,
        702,
        3,
        'Jeans',
        4,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'jeans',
        'Jeans',
        'Denim and jeans collection',
        NOW(),
        NOW()
    ),
    (
        707,
        702,
        3,
        'Shorts & Bermudas',
        5,
        0.00,
        NULL,
        NULL,
        1,
        0,
        0,
        'shorts-bermudas',
        'Shorts & Bermudas',
        'Shorts and bermudas',
        NOW(),
        NOW()
    ),
    -- Tops
    (
        708,
        702,
        3,
        'T-shirts & Tops',
        6,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        't-shirts-tops',
        'T-shirts & Tops',
        'Casual tops and t-shirts',
        NOW(),
        NOW()
    ),
    (
        709,
        702,
        3,
        'Shirts & Blouses',
        7,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'shirts-blouses',
        'Shirts & Blouses',
        'Formal and casual shirts',
        NOW(),
        NOW()
    ),
    -- Knitwear and Sweaters
    (
        710,
        702,
        3,
        'Sweaters & Cardigans',
        8,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'sweaters-cardigans',
        'Sweaters & Cardigans',
        'Warm knitwear collection',
        NOW(),
        NOW()
    ),
    (
        711,
        702,
        3,
        'Sweatshirts & Hoodies',
        9,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'sweatshirts-hoodies',
        'Sweatshirts & Hoodies',
        'Casual sweatshirts and hoodies',
        NOW(),
        NOW()
    ),
    -- Outerwear
    (
        712,
        702,
        3,
        'Jackets & Coats',
        10,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'jackets-coats',
        'Jackets & Coats',
        'Winter and light jackets',
        NOW(),
        NOW()
    ),
    (
        713,
        702,
        3,
        'Jumpsuits & Overalls',
        11,
        0.00,
        NULL,
        NULL,
        1,
        0,
        0,
        'jumpsuits-overalls',
        'Jumpsuits & Overalls',
        'Stylish jumpsuits and overalls',
        NOW(),
        NOW()
    );
-- Women's Lingerie & Sleepwear (Level 3)
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        714,
        702,
        3,
        'Lingerie & Sleepwear',
        12,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'lingerie-sleepwear',
        'Lingerie & Sleepwear',
        'Comfortable lingerie and sleepwear',
        NOW(),
        NOW()
    );
-- Lingerie Subcategories (Level 4)
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        715,
        714,
        4,
        'Bras',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'bras',
        'Bras',
        'Comfortable bras collection',
        NOW(),
        NOW()
    ),
    (
        716,
        714,
        4,
        'Panties & Shorties',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'panties-shorties',
        'Panties & Shorties',
        'Comfortable underwear',
        NOW(),
        NOW()
    ),
    (
        717,
        714,
        4,
        'Pajamas & Nightdresses',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'pajamas-nightdresses',
        'Pajamas & Nightdresses',
        'Comfortable sleepwear',
        NOW(),
        NOW()
    ),
    (
        718,
        714,
        4,
        'Tights & Stockings',
        4,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'tights-stockings',
        'Tights & Stockings',
        'Hosiery collection',
        NOW(),
        NOW()
    );
-- Women's Footwear (Level 2)
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        719,
        701,
        2,
        'Women\'s Footwear',
        2,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'womens-footwear',
        'Women\'s Footwear',
        'Women\'s shoes collection',
        NOW(),
        NOW()
    );
-- Footwear Subcategories (Level 3)
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        720,
        719,
        3,
        'Sneakers',
        1,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'womens-sneakers',
        'Women\'s Sneakers',
        'Casual and sports sneakers',
        NOW(),
        NOW()
    ),
    (
        721,
        719,
        3,
        'Sandals',
        2,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'womens-sandals',
        'Women\'s Sandals',
        'Stylish sandals collection',
        NOW(),
        NOW()
    ),
    (
        722,
        719,
        3,
        'Boots & Booties',
        3,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'boots-booties',
        'Boots & Booties',
        'Winter and fashion boots',
        NOW(),
        NOW()
    ),
    (
        723,
        719,
        3,
        'Loafers & Derbies',
        4,
        0.00,
        NULL,
        NULL,
        1,
        0,
        0,
        'loafers-derbies',
        'Loafers & Derbies',
        'Classic footwear collection',
        NOW(),
        NOW()
    );
-- Women's Accessories (Level 2)
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        724,
        701,
        2,
        'Women\'s Accessories',
        3,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'womens-accessories',
        'Women\'s Accessories',
        'Fashion accessories collection',
        NOW(),
        NOW()
    );
-- Accessories Subcategories (Level 3)
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        725,
        724,
        3,
        'Bags & Clutches',
        1,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'bags-clutches',
        'Bags & Clutches',
        'Stylish bags collection',
        NOW(),
        NOW()
    ),
    (
        726,
        724,
        3,
        'Belts',
        2,
        0.00,
        NULL,
        NULL,
        1,
        0,
        0,
        'womens-belts',
        'Women\'s Belts',
        'Fashion belts',
        NOW(),
        NOW()
    ),
    (
        727,
        724,
        3,
        'Jewelry',
        3,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'jewelry',
        'Jewelry',
        'Fashion jewelry collection',
        NOW(),
        NOW()
    ),
    (
        728,
        724,
        3,
        'Hats & Beanies',
        4,
        0.00,
        NULL,
        NULL,
        1,
        0,
        0,
        'hats-beanies',
        'Hats & Beanies',
        'Head accessories',
        NOW(),
        NOW()
    ),
    (
        729,
        724,
        3,
        'Scarves & Shawls',
        5,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'scarves-shawls',
        'Scarves & Shawls',
        'Elegant scarves collection',
        NOW(),
        NOW()
    );
-- Women's Specialty (Level 2)
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        730,
        701,
        2,
        'Women\'s Specialty',
        4,
        0.00,
        NULL,
        NULL,
        1,
        0,
        0,
        'womens-specialty',
        'Women\'s Specialty',
        'Special size collections',
        NOW(),
        NOW()
    );
-- Specialty Subcategories (Level 3)
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        731,
        730,
        3,
        'Plus Size',
        1,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'plus-size',
        'Plus Size Collection',
        'Stylish plus size fashion',
        NOW(),
        NOW()
    ),
    (
        732,
        730,
        3,
        'Maternity',
        2,
        0.00,
        NULL,
        NULL,
        1,
        1,
        0,
        'maternity',
        'Maternity Collection',
        'Comfortable maternity wear',
        NOW(),
        NOW()
    );
-- 2. Men's Category
-- Similar structure for men's categories (733-760)
-- Will continue with the rest of the categories in the next section...
-- Add translations for all categories
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
SELECT id,
    name,
    'en',
    NOW(),
    NOW()
FROM categories
WHERE id BETWEEN 701 AND 732
UNION ALL
SELECT id,
    CASE
        WHEN name = 'Women' THEN 'Femme'
        WHEN name = 'Women\'s Clothing' THEN 'Vêtements Femme'
        WHEN name = 'Dresses' THEN 'Robes'
        WHEN name = 'Skirts' THEN 'Jupes'
        WHEN name = 'Pants' THEN 'Pantalons'
        WHEN name = 'Jeans' THEN 'Jeans'
        WHEN name = 'Shorts & Bermudas' THEN 'Shorts et Bermudas'
        WHEN name = 'T-shirts & Tops' THEN 'T-shirts et Hauts'
        WHEN name = 'Shirts & Blouses' THEN 'Chemises et Chemisiers'
        WHEN name = 'Sweaters & Cardigans' THEN 'Pulls et Gilets'
        WHEN name = 'Sweatshirts & Hoodies' THEN 'Sweats et Sweats à Capuche'
        WHEN name = 'Jackets & Coats' THEN 'Vestes et Manteaux'
        WHEN name = 'Jumpsuits & Overalls' THEN 'Combinaisons et Salopettes'
        WHEN name = 'Lingerie & Sleepwear' THEN 'Lingerie et Vêtements de Nuit'
        WHEN name = 'Bras' THEN 'Soutiens-gorge'
        WHEN name = 'Panties & Shorties' THEN 'Culottes et Shorties'
        WHEN name = 'Pajamas & Nightdresses' THEN 'Pyjamas et Chemises de Nuit'
        WHEN name = 'Tights & Stockings' THEN 'Collants et Bas'
        WHEN name = 'Women\'s Footwear' THEN 'Chaussures Femme'
        WHEN name = 'Sneakers' THEN 'Baskets'
        WHEN name = 'Sandals' THEN 'Sandales'
        WHEN name = 'Boots & Booties' THEN 'Bottes et Bottines'
        WHEN name = 'Loafers & Derbies' THEN 'Mocassins et Derbies'
        WHEN name = 'Women\'s Accessories' THEN 'Accessoires Femme'
        WHEN name = 'Bags & Clutches' THEN 'Sacs et Pochettes'
        WHEN name = 'Belts' THEN 'Ceintures'
        WHEN name = 'Jewelry' THEN 'Bijoux'
        WHEN name = 'Hats & Beanies' THEN 'Chapeaux et Bonnets'
        WHEN name = 'Scarves & Shawls' THEN 'Écharpes et Châles'
        WHEN name = 'Women\'s Specialty' THEN 'Spécialités Femme'
        WHEN name = 'Plus Size' THEN 'Grande Taille'
        WHEN name = 'Maternity' THEN 'Maternité'
    END,
    'fr',
    NOW(),
    NOW()
FROM categories
WHERE id BETWEEN 701 AND 732
UNION ALL
SELECT id,
    CASE
        WHEN name = 'Women' THEN 'نساء'
        WHEN name = 'Women\'s Clothing' THEN 'ملابس نسائية'
        WHEN name = 'Dresses' THEN 'فساتين'
        WHEN name = 'Skirts' THEN 'تنانير'
        WHEN name = 'Pants' THEN 'بناطيل'
        WHEN name = 'Jeans' THEN 'جينز'
        WHEN name = 'Shorts & Bermudas' THEN 'شورتات وبرمودا'
        WHEN name = 'T-shirts & Tops' THEN 'تي شيرت وقمصان'
        WHEN name = 'Shirts & Blouses' THEN 'قمصان وبلوزات'
        WHEN name = 'Sweaters & Cardigans' THEN 'كنزات وكارديجان'
        WHEN name = 'Sweatshirts & Hoodies' THEN 'سترات وهوديز'
        WHEN name = 'Jackets & Coats' THEN 'جاكيتات ومعاطف'
        WHEN name = 'Jumpsuits & Overalls' THEN 'أفرولات وسالوبيتات'
        WHEN name = 'Lingerie & Sleepwear' THEN 'ملابس داخلية وملابس نوم'
        WHEN name = 'Bras' THEN 'حمالات صدر'
        WHEN name = 'Panties & Shorties' THEN 'سراويل داخلية'
        WHEN name = 'Pajamas & Nightdresses' THEN 'بيجامات وقمصان نوم'
        WHEN name = 'Tights & Stockings' THEN 'جوارب وكولونات'
        WHEN name = 'Women\'s Footwear' THEN 'أحذية نسائية'
        WHEN name = 'Sneakers' THEN 'أحذية رياضية'
        WHEN name = 'Sandals' THEN 'صنادل'
        WHEN name = 'Boots & Booties' THEN 'أحذية وبوتات'
        WHEN name = 'Loafers & Derbies' THEN 'حذاء لوفر وديربي'
        WHEN name = 'Women\'s Accessories' THEN 'إكسسوارات نسائية'
        WHEN name = 'Bags & Clutches' THEN 'حقائب وكلاتش'
        WHEN name = 'Belts' THEN 'أحزمة'
        WHEN name = 'Jewelry' THEN 'مجوهرات'
        WHEN name = 'Hats & Beanies' THEN 'قبعات وطواقي'
        WHEN name = 'Scarves & Shawls' THEN 'أوشحة وشالات'
        WHEN name = 'Women\'s Specialty' THEN 'تخصصات نسائية'
        WHEN name = 'Plus Size' THEN 'مقاسات كبيرة'
        WHEN name = 'Maternity' THEN 'ملابس حمل'
    END,
    'sa',
    NOW(),
    NOW()
FROM categories
WHERE id BETWEEN 701 AND 732;
-- More categories and translations to follow in Part 2...
-- Will include Men's, Kids', Baby, and Special Collections categories
-- Update AUTO_INCREMENT to avoid conflicts
ALTER TABLE `categories` AUTO_INCREMENT = 1000;
ALTER TABLE `category_translations` AUTO_INCREMENT = 3000;
COMMIT;
