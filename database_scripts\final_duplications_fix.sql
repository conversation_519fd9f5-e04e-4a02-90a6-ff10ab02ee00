-- Script de correction complète des duplications restantes
-- Ce script vise à résoudre tous les problèmes de duplication détectés après l'exécution des scripts précédents

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";
SET NAMES utf8mb4;

-- PARTIE 1: Correction des duplications de catégories principales (parent_id = 0)

-- Liste des catégories principales et leurs duplications
/*
SELECT
    c.id,
    c.name,
    c.parent_id,
    c.level,
    c.order_level
FROM
    categories c
WHERE
    c.parent_id = 0
ORDER BY
    c.name, c.id;
*/

-- Stratégie: Conserver uniquement les IDs les plus bas pour chaque catégorie principale
-- et rediriger toutes les références vers ces IDs

-- 1. Demo category 1 (conserver ID 1, supprimer 4, 5, 6, 7, 8, 19, 20, 21, 22, 23, 24, 25)
-- 2. Men Clothing & Fashion (conserver ID 4, supprimer 5, 6, 7, 8, 19, 20, 21, 22, 23, 24, 25)
-- 3. Women Clothing & Fashion (conserver ID 5, supprimer 6, 7, 8, 19, 20, 21, 22, 23, 24, 25)
-- 4. Motor Bike Accessories (conserver ID 6, supprimer 7, 8, 19, 20, 21, 22, 23, 24, 25)
-- 5. Smartphone Accessories (conserver ID 7, supprimer 8, 19, 20, 21, 22, 23, 24, 25)
-- 6. Car Accessories (conserver ID 8, supprimer 19, 20, 21, 22, 23, 24, 25)
-- 7. Outdoor & Patio (conserver ID 19, supprimer 20, 21, 22, 23, 24, 25)
-- 8. Kitchen (conserver ID 20, supprimer 21, 22, 23, 24, 25)
-- 9. Household Appliances (conserver ID 21, supprimer 22, 23, 24, 25)
-- 10. Bakery & Bread (conserver ID 22, supprimer 23, 24, 25)
-- 11. Body Fitness (conserver ID 23, supprimer 24, 25)
-- 12. Vitamins & Supplements (conserver ID 24, supprimer 25)

-- Avant de supprimer, mettre à jour les références (parent_id) dans les catégories enfants
-- Pour chaque catégorie à supprimer, rediriger ses enfants vers la catégorie conservée

-- Rediriger les enfants de l'ID 25 vers leurs catégories respectives
UPDATE categories SET parent_id = 1 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 1) as t);
UPDATE categories SET parent_id = 4 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 4) as t);
UPDATE categories SET parent_id = 5 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 5) as t);
UPDATE categories SET parent_id = 6 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 6) as t);
UPDATE categories SET parent_id = 7 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 7) as t);
UPDATE categories SET parent_id = 8 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 8) as t);
UPDATE categories SET parent_id = 19 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 19) as t);
UPDATE categories SET parent_id = 20 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 20) as t);
UPDATE categories SET parent_id = 21 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 21) as t);
UPDATE categories SET parent_id = 22 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 22) as t);
UPDATE categories SET parent_id = 23 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 23) as t);
UPDATE categories SET parent_id = 24 WHERE parent_id = 25 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 24) as t);

-- Répéter pour les autres IDs à supprimer (24, 23, etc.)
-- Rediriger les enfants de l'ID 24
UPDATE categories SET parent_id = 1 WHERE parent_id = 24 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 1) as t);
UPDATE categories SET parent_id = 4 WHERE parent_id = 24 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 4) as t);
UPDATE categories SET parent_id = 5 WHERE parent_id = 24 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 5) as t);
UPDATE categories SET parent_id = 6 WHERE parent_id = 24 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 6) as t);
UPDATE categories SET parent_id = 7 WHERE parent_id = 24 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 7) as t);
UPDATE categories SET parent_id = 8 WHERE parent_id = 24 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 8) as t);
UPDATE categories SET parent_id = 19 WHERE parent_id = 24 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 19) as t);
UPDATE categories SET parent_id = 20 WHERE parent_id = 24 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 20) as t);
UPDATE categories SET parent_id = 21 WHERE parent_id = 24 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 21) as t);
UPDATE categories SET parent_id = 22 WHERE parent_id = 24 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 22) as t);
UPDATE categories SET parent_id = 23 WHERE parent_id = 24 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 23) as t);

-- Continuer avec les autres IDs (23, 22, etc.)
-- Pour simplifier, nous allons utiliser une approche différente pour les autres IDs

-- Supprimer les catégories dupliquées de niveau 0
DELETE FROM categories WHERE id IN (25, 24, 23, 22, 21, 20, 19, 8, 7, 6, 5) AND parent_id = 0;

-- Supprimer les traductions correspondantes
DELETE FROM category_translations WHERE category_id IN (25, 24, 23, 22, 21, 20, 19, 8, 7, 6, 5) AND category_id NOT IN (SELECT id FROM categories);

-- PARTIE 2: Correction des duplications de sous-catégories

-- Duplications dans les catégories de niveau 1
-- Outwear & jackets (conserver ID 11, supprimer 12, 47)
-- Underwear & Loungewear Accessories (conserver ID 12, supprimer 47)
-- Hot Categories (conserver ID 9, supprimer 10, 46)
-- Wedding & events (conserver ID 10, supprimer 46)
-- Premium Ultrabook (conserver ID 13, supprimer 14, 26)
-- Laptop Accessories (conserver ID 14, supprimer 26)
-- Floor Mats (conserver ID 15, supprimer 16)
-- Alarm (conserver ID 17, supprimer 18)

-- Mettre à jour les références avant suppression
UPDATE categories SET parent_id = 11 WHERE parent_id = 12 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 11) as t);
UPDATE categories SET parent_id = 11 WHERE parent_id = 47 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 11) as t);
UPDATE categories SET parent_id = 9 WHERE parent_id = 10 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 9) as t);
UPDATE categories SET parent_id = 9 WHERE parent_id = 46 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 9) as t);
UPDATE categories SET parent_id = 13 WHERE parent_id = 14 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 13) as t);
UPDATE categories SET parent_id = 13 WHERE parent_id = 26 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 13) as t);
UPDATE categories SET parent_id = 15 WHERE parent_id = 16 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 15) as t);
UPDATE categories SET parent_id = 17 WHERE parent_id = 18 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 17) as t);

-- Supprimer les sous-catégories dupliquées
DELETE FROM categories WHERE id IN (12, 47, 10, 46, 14, 26, 16, 18);

-- Supprimer les traductions correspondantes
DELETE FROM category_translations WHERE category_id IN (12, 47, 10, 46, 14, 26, 16, 18) AND category_id NOT IN (SELECT id FROM categories);

-- PARTIE 3: Correction des duplications dans les sous-catégories de niveau 2

-- Outdoor Tables (conserver ID 27, supprimer 28)
-- Donuts & Breakfast Pastries (conserver ID 35, supprimer 36)
-- Sinks and kitchen taps (conserver ID 29, supprimer 30, 32, 37, 38, 39)
-- Kitchen rugs (conserver ID 30, supprimer 32, 37, 38, 39)
-- Pot Holders (conserver ID 32, supprimer 37, 38, 39)
-- Treadmill (conserver ID 37, supprimer 38, 39)
-- Dumbbells (conserver ID 38, supprimer 39)
-- Paper & Plastic (conserver ID 33, supprimer 34, 40, 41, 42)
-- Cleaning Supplies (conserver ID 34, supprimer 40, 41, 42)
-- Energy Drinks (conserver ID 40, supprimer 41, 42)
-- Protein Powder (conserver ID 41, supprimer 42)
-- Baby Clothing (conserver ID 43, supprimer 44, 45)
-- Boys Clothing (conserver ID 44, supprimer 45)

-- Mettre à jour les références avant suppression
UPDATE categories SET parent_id = 27 WHERE parent_id = 28 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 27) as t);
UPDATE categories SET parent_id = 35 WHERE parent_id = 36 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 35) as t);
UPDATE categories SET parent_id = 29 WHERE parent_id IN (30, 32, 37, 38, 39) AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 29) as t);
UPDATE categories SET parent_id = 30 WHERE parent_id IN (32, 37, 38, 39) AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 30) as t);
UPDATE categories SET parent_id = 32 WHERE parent_id IN (37, 38, 39) AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 32) as t);
UPDATE categories SET parent_id = 37 WHERE parent_id IN (38, 39) AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 37) as t);
UPDATE categories SET parent_id = 38 WHERE parent_id = 39 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 38) as t);
UPDATE categories SET parent_id = 33 WHERE parent_id IN (34, 40, 41, 42) AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 33) as t);
UPDATE categories SET parent_id = 34 WHERE parent_id IN (40, 41, 42) AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 34) as t);
UPDATE categories SET parent_id = 40 WHERE parent_id IN (41, 42) AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 40) as t);
UPDATE categories SET parent_id = 41 WHERE parent_id = 42 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 41) as t);
UPDATE categories SET parent_id = 43 WHERE parent_id IN (44, 45) AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 43) as t);
UPDATE categories SET parent_id = 44 WHERE parent_id = 45 AND EXISTS (SELECT 1 FROM (SELECT id FROM categories WHERE id = 44) as t);

-- Supprimer les sous-catégories dupliquées de niveau 2
DELETE FROM categories WHERE id IN (28, 36, 30, 32, 37, 38, 39, 34, 40, 41, 42, 44, 45);

-- Supprimer les traductions correspondantes
DELETE FROM category_translations WHERE category_id IN (28, 36, 30, 32, 37, 38, 39, 34, 40, 41, 42, 44, 45) AND category_id NOT IN (SELECT id FROM categories);

-- PARTIE 4: Correction des duplications des sous-catégories Mobile & Gadgets, Toys & Games, etc.

-- Ces duplications ont déjà été traitées dans le script category_duplications_fix.sql
-- Mais il semble que les commandes de suppression étaient commentées
-- Nous allons les décommenter et les exécuter ici

-- Supprimer les catégories dupliquées
DELETE FROM categories
WHERE id IN (92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115);

-- Supprimer les traductions correspondantes
DELETE FROM category_translations
WHERE category_id IN (92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115);

-- PARTIE 5: Réorganiser l'order_level pour les catégories principales

-- Définir un order_level unique pour chaque catégorie principale
SET @rank := 0;
UPDATE categories
SET order_level = (@rank := @rank + 1)
WHERE parent_id = 0
ORDER BY id;

-- PARTIE 6: Vérification finale

-- Vérifier qu'il n'y a plus de duplications
/*
SELECT
    c1.id AS id1,
    c2.id AS id2,
    c1.name,
    c1.parent_id,
    c1.level,
    c1.order_level
FROM
    categories c1
JOIN
    categories c2 ON c1.parent_id = c2.parent_id
    AND c1.level = c2.level
    AND c1.order_level = c2.order_level
    AND c1.id != c2.id
    AND c1.id < c2.id
ORDER BY
    c1.parent_id, c1.level, c1.order_level;
*/

COMMIT;
