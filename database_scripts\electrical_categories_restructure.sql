-- Start transaction for safety
START TRANSACTION;
-- First, delete existing electrical-related categories
DELETE FROM category_translations
WHERE category_id IN (93, 300, 301, 302)
    OR category_id BETWEEN 600 AND 650;
DELETE FROM categories
WHERE id IN (93, 300, 301, 302)
    OR id BETWEEN 600 AND 650;
-- Create main Electrical category
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        600,
        0,
        0,
        'Electrical',
        1,
        0.00,
        'uploads/categories/banner/electrical-banner.jpg',
        'uploads/categories/icon/electrical-icon.png',
        1,
        1,
        0,
        'electrical',
        'Electrical',
        'Professional electrical equipment, components, and supplies',
        NOW(),
        NOW()
    );
-- Add translations for main category
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES (600, 'Electrical', 'en', NOW(), NOW()),
    (600, 'Électrique', 'fr', NOW(), NOW()),
    (600, 'كهربائي', 'sa', NOW(), NOW());
-- 1. Electrical Cables Category
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        601,
        600,
        1,
        'Electrical Cables',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'electrical-cables',
        'Electrical Cables',
        'All types of electrical cables and wiring',
        NOW(),
        NOW()
    );
-- Cable subcategories
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        602,
        601,
        2,
        'Power Cables',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'power-cables',
        'Power Cables',
        'Power transmission cables',
        NOW(),
        NOW()
    ),
    (
        603,
        601,
        2,
        'Control Cables',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'control-cables',
        'Control Cables',
        'Control and signal cables',
        NOW(),
        NOW()
    ),
    (
        604,
        601,
        2,
        'Network Cables',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'network-cables',
        'Network Cables',
        'Data and network cables',
        NOW(),
        NOW()
    ),
    (
        605,
        601,
        2,
        'Industrial Cables',
        4,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'industrial-cables',
        'Industrial Cables',
        'Heavy-duty industrial cables',
        NOW(),
        NOW()
    );
-- 2. Circuit Protection Category
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        606,
        600,
        1,
        'Circuit Protection',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'circuit-protection',
        'Circuit Protection',
        'Circuit protection devices and equipment',
        NOW(),
        NOW()
    );
-- Circuit Protection subcategories
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        607,
        606,
        2,
        'Circuit Breakers',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'circuit-breakers',
        'Circuit Breakers',
        'All types of circuit breakers',
        NOW(),
        NOW()
    ),
    (
        608,
        606,
        2,
        'Fuses',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'fuses',
        'Fuses',
        'Electrical fuses and fuse holders',
        NOW(),
        NOW()
    ),
    (
        609,
        606,
        2,
        'Surge Protectors',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'surge-protectors',
        'Surge Protectors',
        'Surge protection devices',
        NOW(),
        NOW()
    );
-- 3. Switches & Controls Category
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        610,
        600,
        1,
        'Switches & Controls',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'switches-controls',
        'Switches & Controls',
        'Electrical switches and control devices',
        NOW(),
        NOW()
    );
-- Switches & Controls subcategories
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        611,
        610,
        2,
        'Light Switches',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'light-switches',
        'Light Switches',
        'Switches for lighting control',
        NOW(),
        NOW()
    ),
    (
        612,
        610,
        2,
        'Dimmers',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'dimmers',
        'Dimmers',
        'Light dimming controls',
        NOW(),
        NOW()
    ),
    (
        613,
        610,
        2,
        'Control Panels',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'control-panels',
        'Control Panels',
        'Electrical control panels',
        NOW(),
        NOW()
    );
-- 4. Power Distribution Category
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        614,
        600,
        1,
        'Power Distribution',
        4,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'power-distribution',
        'Power Distribution',
        'Power distribution equipment and components',
        NOW(),
        NOW()
    );
-- Power Distribution subcategories
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        615,
        614,
        2,
        'Distribution Boxes',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'distribution-boxes',
        'Distribution Boxes',
        'Electrical distribution boxes',
        NOW(),
        NOW()
    ),
    (
        616,
        614,
        2,
        'Panel Boards',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'panel-boards',
        'Panel Boards',
        'Electrical panel boards',
        NOW(),
        NOW()
    ),
    (
        617,
        614,
        2,
        'Transformers',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'transformers',
        'Transformers',
        'Power transformers',
        NOW(),
        NOW()
    );
-- 5. Wiring Devices Category
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        618,
        600,
        1,
        'Wiring Devices',
        5,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'wiring-devices',
        'Wiring Devices',
        'Electrical wiring devices and accessories',
        NOW(),
        NOW()
    );
-- Wiring Devices subcategories
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        619,
        618,
        2,
        'Outlets',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'outlets',
        'Outlets',
        'Electrical outlets and sockets',
        NOW(),
        NOW()
    ),
    (
        620,
        618,
        2,
        'Junction Boxes',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'junction-boxes',
        'Junction Boxes',
        'Electrical junction boxes',
        NOW(),
        NOW()
    ),
    (
        621,
        618,
        2,
        'Conduits',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'conduits',
        'Conduits',
        'Electrical conduits and raceways',
        NOW(),
        NOW()
    );
-- Add translations for all categories
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
SELECT id,
    name,
    'en',
    NOW(),
    NOW()
FROM categories
WHERE id BETWEEN 601 AND 621
UNION ALL
SELECT id,
    CASE
        WHEN name = 'Electrical Cables' THEN 'Câbles Électriques'
        WHEN name = 'Power Cables' THEN 'Câbles d''Alimentation'
        WHEN name = 'Control Cables' THEN 'Câbles de Contrôle'
        WHEN name = 'Network Cables' THEN 'Câbles Réseau'
        WHEN name = 'Industrial Cables' THEN 'Câbles Industriels'
        WHEN name = 'Circuit Protection' THEN 'Protection des Circuits'
        WHEN name = 'Circuit Breakers' THEN 'Disjoncteurs'
        WHEN name = 'Fuses' THEN 'Fusibles'
        WHEN name = 'Surge Protectors' THEN 'Parasurtenseurs'
        WHEN name = 'Switches & Controls' THEN 'Interrupteurs et Contrôles'
        WHEN name = 'Light Switches' THEN 'Interrupteurs'
        WHEN name = 'Dimmers' THEN 'Variateurs'
        WHEN name = 'Control Panels' THEN 'Panneaux de Contrôle'
        WHEN name = 'Power Distribution' THEN 'Distribution Électrique'
        WHEN name = 'Distribution Boxes' THEN 'Boîtes de Distribution'
        WHEN name = 'Panel Boards' THEN 'Tableaux Électriques'
        WHEN name = 'Transformers' THEN 'Transformateurs'
        WHEN name = 'Wiring Devices' THEN 'Dispositifs de Câblage'
        WHEN name = 'Outlets' THEN 'Prises'
        WHEN name = 'Junction Boxes' THEN 'Boîtes de Jonction'
        WHEN name = 'Conduits' THEN 'Conduits'
    END,
    'fr',
    NOW(),
    NOW()
FROM categories
WHERE id BETWEEN 601 AND 621
UNION ALL
SELECT id,
    CASE
        WHEN name = 'Electrical Cables' THEN 'كابلات كهربائية'
        WHEN name = 'Power Cables' THEN 'كابلات الطاقة'
        WHEN name = 'Control Cables' THEN 'كابلات التحكم'
        WHEN name = 'Network Cables' THEN 'كابلات الشبكة'
        WHEN name = 'Industrial Cables' THEN 'كابلات صناعية'
        WHEN name = 'Circuit Protection' THEN 'حماية الدوائر'
        WHEN name = 'Circuit Breakers' THEN 'قواطع الدائرة'
        WHEN name = 'Fuses' THEN 'المصهرات'
        WHEN name = 'Surge Protectors' THEN 'حماية من التيار المفاجئ'
        WHEN name = 'Switches & Controls' THEN 'مفاتيح وأدوات التحكم'
        WHEN name = 'Light Switches' THEN 'مفاتيح الإضاءة'
        WHEN name = 'Dimmers' THEN 'خافتات الإضاءة'
        WHEN name = 'Control Panels' THEN 'لوحات التحكم'
        WHEN name = 'Power Distribution' THEN 'توزيع الطاقة'
        WHEN name = 'Distribution Boxes' THEN 'صناديق التوزيع'
        WHEN name = 'Panel Boards' THEN 'لوحات التوزيع'
        WHEN name = 'Transformers' THEN 'المحولات'
        WHEN name = 'Wiring Devices' THEN 'أجهزة التوصيل'
        WHEN name = 'Outlets' THEN 'المنافذ'
        WHEN name = 'Junction Boxes' THEN 'صناديق التوصيل'
        WHEN name = 'Conduits' THEN 'القنوات'
    END,
    'sa',
    NOW(),
    NOW()
FROM categories
WHERE id BETWEEN 601 AND 621;
-- Update AUTO_INCREMENT to avoid conflicts
ALTER TABLE `categories` AUTO_INCREMENT = 650;
ALTER TABLE `category_translations` AUTO_INCREMENT = 2000;
COMMIT;
