-- Start transaction for safety
START TRANSACTION;

-- First, properly clean up any existing categories in our range
DELETE FROM category_translations
WHERE category_id >= 400 AND category_id <= 500;

DELETE FROM categories
WHERE id >= 400 AND id <= 500;
-- Create main Information Technology category
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        400,
        0,
        0,
        'Information Technology',
        1,
        0.00,
        'uploads/categories/banner/it.jpg',
        'uploads/categories/icon/it.png',
        1,
        1,
        0,
        'information-technology',
        'Information Technology',
        'Computers, laptops, accessories and more',
        NOW(),
        NOW()
    );
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES (
        400,
        'Information Technology',
        'en',
        NOW(),
        NOW()
    ),
    (400, 'Informatique', 'fr', NOW(), NOW()),
    (400, 'تكنولوجيا المعلومات', 'sa', NOW(), NOW());
-- 1. Laptops Category
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        401,
        400,
        1,
        'Laptops',
        1,
        0.00,
        'uploads/categories/banner/laptops.jpg',
        'uploads/categories/icon/laptops.png',
        1,
        1,
        0,
        'laptops',
        'Laptops',
        'Professional, Gaming and Personal Laptops',
        NOW(),
        NOW()
    );
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES (401, 'Laptops', 'en', NOW(), NOW()),
    (401, 'PC Portables', 'fr', NOW(), NOW()),
    (
        401,
        'أجهزة الكمبيوتر المحمولة',
        'sa',
        NOW(),
        NOW()
    );
-- Laptop subcategories
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        402,
        401,
        2,
        'Professional Laptops',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'professional-laptops',
        'Professional Laptops',
        'Business and Professional Laptops',
        NOW(),
        NOW()
    ),
    (
        403,
        401,
        2,
        'Gaming Laptops',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'gaming-laptops',
        'Gaming Laptops',
        'High-performance Gaming Laptops',
        NOW(),
        NOW()
    ),
    (
        404,
        401,
        2,
        'Mini Laptops',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'mini-laptops',
        'Mini Laptops',
        'Compact and Portable Laptops',
        NOW(),
        NOW()
    ),
    (
        405,
        401,
        2,
        'MacBook Air',
        4,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'macbook-air',
        'MacBook Air',
        'Apple MacBook Air Laptops',
        NOW(),
        NOW()
    ),
    (
        406,
        401,
        2,
        'MacBook Pro',
        5,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'macbook-pro',
        'MacBook Pro',
        'Apple MacBook Pro Laptops',
        NOW(),
        NOW()
    );
-- Translations for laptop subcategories
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES -- Professional Laptops
    (402, 'Professional Laptops', 'en', NOW(), NOW()),
    (402, 'Laptop Professionnel', 'fr', NOW(), NOW()),
    (
        402,
        'أجهزة الكمبيوتر المحمولة المهنية',
        'sa',
        NOW(),
        NOW()
    ),
    -- Gaming Laptops
    (403, 'Gaming Laptops', 'en', NOW(), NOW()),
    (403, 'Laptop Gamer', 'fr', NOW(), NOW()),
    (
        403,
        'أجهزة الكمبيوتر المحمولة للألعاب',
        'sa',
        NOW(),
        NOW()
    ),
    -- Mini Laptops
    (404, 'Mini Laptops', 'en', NOW(), NOW()),
    (404, 'Mini Laptop', 'fr', NOW(), NOW()),
    (
        404,
        'أجهزة الكمبيوتر المحمولة الصغيرة',
        'sa',
        NOW(),
        NOW()
    ),
    -- MacBook Air
    (405, 'MacBook Air', 'en', NOW(), NOW()),
    (405, 'MacBook Air', 'fr', NOW(), NOW()),
    (405, 'ماك بوك اير', 'sa', NOW(), NOW()),
    -- MacBook Pro
    (406, 'MacBook Pro', 'en', NOW(), NOW()),
    (406, 'MacBook Pro', 'fr', NOW(), NOW()),
    (406, 'ماك بوك برو', 'sa', NOW(), NOW());
-- 2. Desktop Computers Category
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        407,
        400,
        1,
        'Desktop Computers',
        2,
        0.00,
        'uploads/categories/banner/desktops.jpg',
        'uploads/categories/icon/desktops.png',
        1,
        0,
        0,
        'desktop-computers',
        'Desktop Computers',
        'Desktop PCs and Workstations',
        NOW(),
        NOW()
    );
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES (407, 'Desktop Computers', 'en', NOW(), NOW()),
    (407, 'PC de Bureau', 'fr', NOW(), NOW()),
    (
        407,
        'أجهزة الكمبيوتر المكتبية',
        'sa',
        NOW(),
        NOW()
    );
-- Desktop subcategories
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        408,
        407,
        2,
        'Desktop PC',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'desktop-pc',
        'Desktop PC',
        'Standard Desktop Computers',
        NOW(),
        NOW()
    ),
    (
        409,
        407,
        2,
        'Gaming PC',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'gaming-pc',
        'Gaming PC',
        'High-performance Gaming Computers',
        NOW(),
        NOW()
    ),
    (
        410,
        407,
        2,
        'All-In-One PC',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'all-in-one-pc',
        'All-In-One PC',
        'Integrated Desktop Computers',
        NOW(),
        NOW()
    ),
    (
        411,
        407,
        2,
        'iMac',
        4,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'imac',
        'iMac',
        'Apple iMac Computers',
        NOW(),
        NOW()
    );
-- Translations for desktop subcategories
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES -- Desktop PC
    (408, 'Desktop PC', 'en', NOW(), NOW()),
    (408, 'PC Fixe', 'fr', NOW(), NOW()),
    (408, 'كمبيوتر مكتبي', 'sa', NOW(), NOW()),
    -- Gaming PC
    (409, 'Gaming PC', 'en', NOW(), NOW()),
    (409, 'PC Gamer', 'fr', NOW(), NOW()),
    (409, 'كمبيوتر للألعاب', 'sa', NOW(), NOW()),
    -- All-In-One PC
    (410, 'All-In-One PC', 'en', NOW(), NOW()),
    (410, 'All In One', 'fr', NOW(), NOW()),
    (410, 'كمبيوتر متكامل', 'sa', NOW(), NOW()),
    -- iMac
    (411, 'iMac', 'en', NOW(), NOW()),
    (411, 'iMac', 'fr', NOW(), NOW()),
    (411, 'آي ماك', 'sa', NOW(), NOW());
-- Continue with other main categories...
-- 3. Computer Components
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        412,
        400,
        1,
        'Computer Components',
        3,
        0.00,
        'uploads/categories/banner/components.jpg',
        'uploads/categories/icon/components.png',
        1,
        0,
        0,
        'computer-components',
        'Computer Components',
        'PC Parts and Components',
        NOW(),
        NOW()
    );
-- Add computer components subcategories (RAM, CPU, Motherboard, etc.)
-- 4. Computer Peripherals
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        413,
        400,
        1,
        'Computer Peripherals',
        4,
        0.00,
        'uploads/categories/banner/peripherals.jpg',
        'uploads/categories/icon/peripherals.png',
        1,
        0,
        0,
        'computer-peripherals',
        'Computer Peripherals',
        'Monitors, Printers, and Other Peripherals',
        NOW(),
        NOW()
    );
-- 5. Gaming & Console
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        414,
        400,
        1,
        'Gaming & Console',
        5,
        0.00,
        'uploads/categories/banner/gaming.jpg',
        'uploads/categories/icon/gaming.png',
        1,
        0,
        0,
        'gaming-console',
        'Gaming & Console',
        'Gaming Consoles and Accessories',
        NOW(),
        NOW()
    );
-- Update AUTO_INCREMENT to prevent conflicts
ALTER TABLE `categories` AUTO_INCREMENT = 500;
ALTER TABLE `category_translations` AUTO_INCREMENT = 1500;
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES -- RAM
    (
        415,
        412,
        2,
        'RAM Memory',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'ram-memory',
        'RAM Memory',
        'Computer RAM and Memory Modules',
        NOW(),
        NOW()
    ),
    -- Processor
    (
        416,
        412,
        2,
        'Processors',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'processors',
        'Processors',
        'CPU and Processors',
        NOW(),
        NOW()
    ),
    -- Motherboard
    (
        417,
        412,
        2,
        'Motherboards',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'motherboards',
        'Motherboards',
        'Computer Motherboards',
        NOW(),
        NOW()
    ),
    -- Graphics Card
    (
        418,
        412,
        2,
        'Graphics Cards',
        4,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'graphics-cards',
        'Graphics Cards',
        'GPU and Graphics Cards',
        NOW(),
        NOW()
    ),
    -- Power Supply
    (
        419,
        412,
        2,
        'Power Supply',
        5,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'power-supply',
        'Power Supply',
        'Computer Power Supply Units',
        NOW(),
        NOW()
    ),
    -- PC Case
    (
        420,
        412,
        2,
        'PC Cases',
        6,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'pc-cases',
        'PC Cases',
        'Computer Cases and Chassis',
        NOW(),
        NOW()
    ),
    -- Cooling Systems
    (
        421,
        412,
        2,
        'Cooling Systems',
        7,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'cooling-systems',
        'Cooling Systems',
        'PC Fans and Cooling Solutions',
        NOW(),
        NOW()
    );
-- Add translations for Computer Components
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES -- RAM
    (415, 'RAM Memory', 'en', NOW(), NOW()),
    (415, 'Mémoire RAM', 'fr', NOW(), NOW()),
    (415, 'ذاكرة رام', 'sa', NOW(), NOW()),
    -- Processor
    (416, 'Processors', 'en', NOW(), NOW()),
    (416, 'Processeurs', 'fr', NOW(), NOW()),
    (416, 'المعالجات', 'sa', NOW(), NOW()),
    -- Motherboard
    (417, 'Motherboards', 'en', NOW(), NOW()),
    (417, 'Cartes Mères', 'fr', NOW(), NOW()),
    (417, 'اللوحات الأم', 'sa', NOW(), NOW()),
    -- Graphics Card
    (418, 'Graphics Cards', 'en', NOW(), NOW()),
    (418, 'Cartes Graphiques', 'fr', NOW(), NOW()),
    (418, 'بطاقات الرسومات', 'sa', NOW(), NOW()),
    -- Power Supply
    (419, 'Power Supply', 'en', NOW(), NOW()),
    (419, 'Alimentation PC', 'fr', NOW(), NOW()),
    (419, 'وحدة التغذية', 'sa', NOW(), NOW()),
    -- PC Case
    (420, 'PC Cases', 'en', NOW(), NOW()),
    (420, 'Boîtiers PC', 'fr', NOW(), NOW()),
    (420, 'علب الكمبيوتر', 'sa', NOW(), NOW()),
    -- Cooling Systems
    (421, 'Cooling Systems', 'en', NOW(), NOW()),
    (
        421,
        'Systèmes de Refroidissement',
        'fr',
        NOW(),
        NOW()
    ),
    (421, 'أنظمة التبريد', 'sa', NOW(), NOW());
-- Add Peripherals subcategories
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES -- Monitors
    (
        422,
        413,
        2,
        'Monitors',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'monitors',
        'Monitors',
        'Computer Monitors and Displays',
        NOW(),
        NOW()
    ),
    -- Printers
    (
        423,
        413,
        2,
        'Printers',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'printers',
        'Printers',
        'Printers and Printing Solutions',
        NOW(),
        NOW()
    ),
    -- Scanners
    (
        424,
        413,
        2,
        'Scanners',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'scanners',
        'Scanners',
        'Document and Image Scanners',
        NOW(),
        NOW()
    ),
    -- Keyboards
    (
        425,
        413,
        2,
        'Keyboards',
        4,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'keyboards',
        'Keyboards',
        'Computer Keyboards',
        NOW(),
        NOW()
    ),
    -- Mice
    (
        426,
        413,
        2,
        'Computer Mice',
        5,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'computer-mice',
        'Computer Mice',
        'Computer Mice and Pointing Devices',
        NOW(),
        NOW()
    ),
    -- Webcams
    (
        427,
        413,
        2,
        'Webcams',
        6,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'webcams',
        'Webcams',
        'Web Cameras and Video Capture',
        NOW(),
        NOW()
    );
-- Add translations for Peripherals
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES -- Monitors
    (422, 'Monitors', 'en', NOW(), NOW()),
    (422, 'Écrans', 'fr', NOW(), NOW()),
    (422, 'شاشات', 'sa', NOW(), NOW()),
    -- Printers
    (423, 'Printers', 'en', NOW(), NOW()),
    (423, 'Imprimantes', 'fr', NOW(), NOW()),
    (423, 'طابعات', 'sa', NOW(), NOW()),
    -- Scanners
    (424, 'Scanners', 'en', NOW(), NOW()),
    (424, 'Scanners', 'fr', NOW(), NOW()),
    (424, 'ماسحات ضوئية', 'sa', NOW(), NOW()),
    -- Keyboards
    (425, 'Keyboards', 'en', NOW(), NOW()),
    (425, 'Claviers', 'fr', NOW(), NOW()),
    (425, 'لوحات المفاتيح', 'sa', NOW(), NOW()),
    -- Mice
    (426, 'Computer Mice', 'en', NOW(), NOW()),
    (426, 'Souris', 'fr', NOW(), NOW()),
    (426, 'فأرات الكمبيوتر', 'sa', NOW(), NOW()),
    -- Webcams
    (427, 'Webcams', 'en', NOW(), NOW()),
    (427, 'Webcams', 'fr', NOW(), NOW()),
    (427, 'كاميرات الويب', 'sa', NOW(), NOW());
-- Add Gaming & Console subcategories
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES -- Consoles
    (
        428,
        414,
        2,
        'Gaming Consoles',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'gaming-consoles',
        'Gaming Consoles',
        'Video Game Consoles',
        NOW(),
        NOW()
    ),
    -- Games
    (
        429,
        414,
        2,
        'Video Games',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'video-games',
        'Video Games',
        'Games for Various Platforms',
        NOW(),
        NOW()
    ),
    -- Gaming Accessories
    (
        430,
        414,
        2,
        'Gaming Accessories',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'gaming-accessories',
        'Gaming Accessories',
        'Gaming Peripherals and Accessories',
        NOW(),
        NOW()
    );
-- Add Gaming subcategories translations
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES -- Gaming Consoles
    (428, 'Gaming Consoles', 'en', NOW(), NOW()),
    (428, 'Consoles de Jeux', 'fr', NOW(), NOW()),
    (428, 'وحدات تحكم الألعاب', 'sa', NOW(), NOW()),
    -- Video Games
    (429, 'Video Games', 'en', NOW(), NOW()),
    (429, 'Jeux Vidéo', 'fr', NOW(), NOW()),
    (429, 'ألعاب الفيديو', 'sa', NOW(), NOW()),
    -- Gaming Accessories
    (430, 'Gaming Accessories', 'en', NOW(), NOW()),
    (430, 'Accessoires Gaming', 'fr', NOW(), NOW()),
    (430, 'ملحقات الألعاب', 'sa', NOW(), NOW());
-- Add Console subcategories
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES -- PlayStation
    (
        431,
        428,
        3,
        'PlayStation',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'playstation',
        'PlayStation',
        'PlayStation Consoles and Games',
        NOW(),
        NOW()
    ),
    -- Xbox
    (
        432,
        428,
        3,
        'Xbox',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'xbox',
        'Xbox',
        'Xbox Consoles and Games',
        NOW(),
        NOW()
    ),
    -- Nintendo
    (
        433,
        428,
        3,
        'Nintendo',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'nintendo',
        'Nintendo',
        'Nintendo Consoles and Games',
        NOW(),
        NOW()
    );
-- Add Console translations
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES -- PlayStation
    (431, 'PlayStation', 'en', NOW(), NOW()),
    (431, 'PlayStation', 'fr', NOW(), NOW()),
    (431, 'بلايستيشن', 'sa', NOW(), NOW()),
    -- Xbox
    (432, 'Xbox', 'en', NOW(), NOW()),
    (432, 'Xbox', 'fr', NOW(), NOW()),
    (432, 'إكس بوكس', 'sa', NOW(), NOW()),
    -- Nintendo
    (433, 'Nintendo', 'en', NOW(), NOW()),
    (433, 'Nintendo', 'fr', NOW(), NOW()),
    (433, 'نينتندو', 'sa', NOW(), NOW());
-- Add Storage Devices category
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES (
        434,
        400,
        1,
        'Storage Devices',
        6,
        0.00,
        'uploads/categories/banner/storage.jpg',
        'uploads/categories/icon/storage.png',
        1,
        0,
        0,
        'storage-devices',
        'Storage Devices',
        'Computer Storage Solutions',
        NOW(),
        NOW()
    );
-- Add Storage subcategories
INSERT INTO `categories` (
        `id`,
        `parent_id`,
        `level`,
        `name`,
        `order_level`,
        `commision_rate`,
        `banner`,
        `icon`,
        `featured`,
        `top`,
        `digital`,
        `slug`,
        `meta_title`,
        `meta_description`,
        `created_at`,
        `updated_at`
    )
VALUES -- External Hard Drives
    (
        435,
        434,
        2,
        'External Hard Drives',
        1,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'external-hard-drives',
        'External Hard Drives',
        'Portable Storage Drives',
        NOW(),
        NOW()
    ),
    -- Internal Hard Drives
    (
        436,
        434,
        2,
        'Internal Hard Drives',
        2,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'internal-hard-drives',
        'Internal Hard Drives',
        'Internal Storage Drives',
        NOW(),
        NOW()
    ),
    -- USB Flash Drives
    (
        437,
        434,
        2,
        'USB Flash Drives',
        3,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'usb-flash-drives',
        'USB Flash Drives',
        'Portable USB Storage',
        NOW(),
        NOW()
    ),
    -- Memory Cards
    (
        438,
        434,
        2,
        'Memory Cards',
        4,
        0.00,
        NULL,
        NULL,
        0,
        0,
        0,
        'memory-cards',
        'Memory Cards',
        'Storage Cards for Devices',
        NOW(),
        NOW()
    );
-- Add Storage translations
INSERT INTO `category_translations` (
        `category_id`,
        `name`,
        `lang`,
        `created_at`,
        `updated_at`
    )
VALUES (434, 'Storage Devices', 'en', NOW(), NOW()),
    (
        434,
        'Périphériques de Stockage',
        'fr',
        NOW(),
        NOW()
    ),
    (434, 'أجهزة التخزين', 'sa', NOW(), NOW()),
    -- External Hard Drives
    (435, 'External Hard Drives', 'en', NOW(), NOW()),
    (435, 'Disques Durs Externes', 'fr', NOW(), NOW()),
    (
        435,
        'الأقراص الصلبة الخارجية',
        'sa',
        NOW(),
        NOW()
    ),
    -- Internal Hard Drives
    (436, 'Internal Hard Drives', 'en', NOW(), NOW()),
    (436, 'Disques Durs Internes', 'fr', NOW(), NOW()),
    (
        436,
        'الأقراص الصلبة الداخلية',
        'sa',
        NOW(),
        NOW()
    ),
    -- USB Flash Drives
    (437, 'USB Flash Drives', 'en', NOW(), NOW()),
    (437, 'Clés USB', 'fr', NOW(), NOW()),
    (437, 'فلاش USB', 'sa', NOW(), NOW()),
    -- Memory Cards
    (438, 'Memory Cards', 'en', NOW(), NOW()),
    (438, 'Cartes Mémoire', 'fr', NOW(), NOW()),
    (438, 'بطاقات الذاكرة', 'sa', NOW(), NOW());
-- Update AUTO_INCREMENT values
ALTER TABLE `categories` AUTO_INCREMENT = 500;
ALTER TABLE `category_translations` AUTO_INCREMENT = 1500;
COMMIT;
